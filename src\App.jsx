import React, { useEffect, useState } from 'react';
import AOS from 'aos';
import 'aos/dist/aos.css';
import gsap from 'gsap';
import { ScrollToPlugin } from 'gsap/ScrollToPlugin';
import { AboutImage, CVImage } from './assets';
import { FaFigma, FaReact, FaJs, FaWhatsapp, FaGithub, FaLinkedin, FaEnvelope, FaHeart } from "react-icons/fa";
import { BiLogoTailwindCss } from "react-icons/bi";
import { VscVscode } from "react-icons/vsc";

gsap.registerPlugin(ScrollToPlugin);

function App() {
  const [nama, setNama] = useState("");
  const [pesan, setPesan] = useState("");

  const handleSubmit = (e) => {
    e.preventDefault();
    const noWhatsApp = "+6283846795074"; // Ganti dengan nomor tujuan tanpa +
    const teks = `Halo, saya ${nama}.\n${pesan}`;
    const url = `https://api.whatsapp.com/send?phone=${noWhatsApp}&text=${encodeURIComponent(teks)}`;
    window.open(url, "_blank");
  };

  useEffect(() => {
    AOS.init({ duration: 1000, once: true });
  }, []);

  useEffect(() => {
    // Scroll animation
    const links = document.querySelectorAll("nav a[href^='#']");
    links.forEach(link => {
      link.addEventListener('click', e => {
        e.preventDefault();
        const target = document.querySelector(link.getAttribute('href'));
        if (target) {
          gsap.to(window, {
            duration: 1,
            scrollTo: { y: target, offsetY: 50 },
            ease: "power2.out"
          });
        }
      });
    });

    return () => {
      links.forEach(link => {
        link.removeEventListener('click', () => { });
      });
    };
  }, []);

  const tools = [
    <FaFigma />, <VscVscode />, <FaReact />, <BiLogoTailwindCss />, <FaJs />, <FaGithub />,
  ];

  const projects = [
    { title: 'Math Brain', desc: 'Lorem ipsum dolor sit amet, consec.', link: '#' },
    { title: 'NatureQuest', desc: 'Lorem ipsum dolor sit amet, consec.', link: '#' },
    { title: 'Tangkap Lalat', desc: 'Lorem ipsum dolor sit amet, consec.', link: '#' }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-100 via-orange-50 to-yellow-50 font-sans text-gray-800 relative overflow-hidden">
      {/* Background Decorative Elements */}
      <div className="absolute inset-0 pointer-events-none">
        {/* Plant/leaf decorations */}
        <div className="absolute top-1/4 left-0 w-16 h-32 bg-green-200 rounded-full transform -rotate-12 opacity-30"></div>
        <div className="absolute bottom-1/4 right-0 w-20 h-40 bg-green-200 rounded-full transform rotate-12 opacity-30"></div>
      </div>

      {/* Navbar */}
      <nav className="sticky top-0 z-50 bg-white/80 backdrop-blur-sm shadow-sm py-4 px-6 flex justify-between items-center">
        <div className="flex items-center">
          <span className="text-xl font-bold text-red-500">Portfolio</span>
        </div>
        <div className="flex items-center gap-6">
          <div className="hidden md:flex gap-6 text-sm font-medium text-gray-700">
            <a href="#home" className="flex items-center gap-1 hover:text-red-500 transition">
              Beranda
            </a>
            <a href="#about" className="flex items-center gap-1 hover:text-yellow-600 transition">
              Tentang
            </a>
            <a href="#skills" className="flex items-center gap-1 hover:text-blue-500 transition">
              Keahlian
            </a>
            <a href="#projects" className="flex items-center gap-1 hover:text-purple-500 transition">
              Proyek
            </a>
            <a href="#contact" className="flex items-center gap-1 hover:text-pink-500 transition">
              <span className="text-pink-500">📞</span> Kontak
            </a>
          </div>
          <button className="bg-pink-400 text-white px-4 py-2 rounded-full text-sm font-medium hover:bg-pink-500 transition">
            Kecil →
          </button>
        </div>
      </nav>

      {/* Hero Section */}
      <section id="home" className="relative flex flex-col-reverse lg:flex-row items-center justify-between px-8 py-16 gap-12 max-w-7xl mx-auto">
        <div className="flex-1 max-w-2xl" data-aos="fade-right">
          <div className="mb-6">
            <h1 className="text-5xl lg:text-6xl font-bold mb-4 leading-tight">
              <span className="text-gray-800">Halo,</span><br />
              <span className="text-red-500">Saya Developer</span><br />
              <span className="text-red-500">Super Lucu!</span>
              <span className="inline-block ml-2">🎉</span>
            </h1>
          </div>

          <p className="text-gray-600 mb-2 text-lg">
            Menciptakan aplikasi keren sambil ketawa-ketiwi!
          </p>
          <p className="text-gray-600 mb-8 flex items-center gap-2">
            <span>🍔</span>
            <span>Coding itu seru, apalagi kalau sambil ngemil!</span>
            <span>🍿</span>
          </p>

          <div className="flex flex-col sm:flex-row gap-4">
            <button className="bg-red-500 text-white px-6 py-3 rounded-lg font-medium hover:bg-red-600 transition-colors flex items-center gap-2">
              <span>👀</span> Lihat Proyek Keren
            </button>
            <button className="border-2 border-red-500 text-red-500 px-6 py-3 rounded-lg font-medium hover:bg-red-50 transition-colors flex items-center gap-2">
              <span>💬</span> Ngobrol Yuk!
            </button>
          </div>
        </div>

        {/* Character Illustration */}
        <div className="flex-1 flex justify-center lg:justify-end" data-aos="zoom-in">
          <div className="relative">
            {/* Yellow speech bubble */}
            <div className="absolute -top-8 -left-8 bg-yellow-300 px-4 py-2 rounded-full text-sm font-medium shadow-lg">
              Halo! 👋
            </div>

            {/* Main character circle */}
            <div className="w-80 h-80 bg-white rounded-full shadow-2xl flex items-center justify-center relative overflow-hidden">
              {/* Character illustration placeholder - you can replace this with actual image */}
              <div className="text-center">
                <div className="text-6xl mb-4">👨‍💻</div>
                <div className="text-lg font-medium text-gray-700">Developer</div>
                <div className="text-sm text-gray-500">Super Lucu!</div>
              </div>

              {/* Laptop illustration */}
              <div className="absolute bottom-8 right-8 text-3xl">💻</div>
              {/* Coffee cup */}
              <div className="absolute bottom-12 left-8 text-2xl">☕</div>
            </div>

            {/* Code icon */}
            <div className="absolute -bottom-4 -right-4 bg-gray-800 text-white p-3 rounded-full shadow-lg">
              <span className="text-xl">&lt;/&gt;</span>
            </div>

            {/* Music note */}
            <div className="absolute top-1/4 -right-8 text-2xl">🎵</div>
          </div>
        </div>

        {/* Floating elements around hero */}
        <div className="absolute top-20 right-20 bg-yellow-300 px-3 py-1 rounded-full text-sm font-medium shadow-lg animate-bounce">
          😊
        </div>
        <div className="absolute bottom-20 left-20 bg-green-300 px-3 py-1 rounded-full text-sm font-medium shadow-lg">
          Hi!
        </div>
      </section>

      {/* Skills Section */}
      <section id="skills" className="px-8 py-16 bg-white/50 backdrop-blur-sm">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-3xl font-bold text-center mb-12 text-gray-800" data-aos="fade-down">
            Keahlian Saya
          </h2>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
            {tools.map((icon, index) => (
              <div
                key={index}
                className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 text-center"
                data-aos="zoom-in"
                data-aos-delay={index * 100}
              >
                <div className="text-4xl text-blue-500 mb-3 flex justify-center">
                  {icon}
                </div>
                <div className="text-sm font-medium text-gray-700">
                  {['Figma', 'VS Code', 'React', 'Tailwind', 'JavaScript', 'GitHub'][index]}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* About */}
      <section id="about" className="px-8 py-16 md:py-26 bg-gradient-to-r from-pink-50 to-orange-50">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-center mb-12 text-gray-800" data-aos="fade-down">Tentang Saya</h2>
          <div className="flex flex-col md:flex-row items-center gap-10">
            <img
              src={AboutImage}
              alt="About"
              className="w-60 h-60 object-cover rounded-full border-4 border-pink-200 shadow-xl"
              data-aos="zoom-in"
            />
            <div className="flex-1">
              <p className="mb-6 text-lg text-gray-700" data-aos="fade-left">
                Aku suka bikin hal lucu & Interaktif <FaHeart className="inline mr-1 text-red-500" /> <br />
                Seperti halaman ini contohnya! Coding buat aku itu seperti bermain puzzle yang seru.
                Setiap baris kode adalah petualangan baru! 🚀
              </p>
              <p className="mb-6 text-gray-600" data-aos="fade-left" data-aos-delay="200">
                Ketika tidak sedang coding, aku suka menggambar, mendengarkan musik, dan tentunya...
                makan cemilan sambil mikirin solusi bug yang bandel! 🍿
              </p>
              <div className="bg-white p-4 rounded-lg shadow-md" data-aos="fade-up">
                <p className="text-sm text-gray-600 italic">
                  "Kode yang indah, lahir dari <strong className="text-red-500">ketekunan</strong> dan secangkir kopi ☕"
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>


      {/* Projects */}
      <section id="projects" className="px-8 md:px-16 py-16 bg-white">
        <h2 className="text-3xl font-bold text-center mb-12 text-gray-800" data-aos="fade-down">Proyek Keren Saya</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {projects.map((project, idx) => (
            <div key={idx} className="bg-gradient-to-br from-pink-50 to-orange-50 p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 border border-pink-100" data-aos="zoom-in" data-aos-delay={idx * 100}>
              <div className="text-3xl mb-4">
                {['🧮', '🌿', '🪰'][idx]}
              </div>
              <h3 className="font-bold text-xl mb-3 text-gray-800">{project.title}</h3>
              <p className="text-gray-600 mb-6">{project.desc}</p>
              <a href={project.link} className="inline-block px-6 py-3 text-sm bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors font-medium">
                Lihat Proyek 👀
              </a>
            </div>
          ))}
        </div>
      </section>

      {/* Contact */}
      <section id="contact" className="px-8 md:px-20 py-16 bg-gradient-to-br from-pink-100 via-orange-50 to-yellow-50">
        <h2 className="text-3xl font-bold text-center mb-12 text-gray-800" data-aos="fade-up">Kontak Saya</h2>

        <div className="flex flex-col md:flex-row gap-10 justify-center items-start max-w-5xl mx-auto">
          <div className="flex flex-col gap-6 w-full md:w-1/2" data-aos="fade-right">
            <div className="bg-white p-6 rounded-xl shadow-lg">
              <h3 className="text-xl font-bold mb-4 text-gray-800">Mari Ngobrol! 💬</h3>
              <p className="text-gray-600 mb-6">
                Punya ide keren? Atau mau sekadar say hi? Jangan ragu untuk menghubungi saya!
              </p>

              <div className="space-y-4">
                <a href="https://wa.me/+6283846795074" className="flex items-center gap-4 p-3 rounded-lg hover:bg-green-50 transition group">
                  <div className="bg-green-100 p-2 rounded-full group-hover:bg-green-200 transition">
                    <FaWhatsapp size={20} className="text-green-600" />
                  </div>
                  <div>
                    <div className="font-medium text-gray-800">WhatsApp</div>
                    <div className="text-sm text-gray-600">+62 838-4679-5074</div>
                  </div>
                </a>

                <a href="https://github.com/your_username" className="flex items-center gap-4 p-3 rounded-lg hover:bg-gray-50 transition group">
                  <div className="bg-gray-100 p-2 rounded-full group-hover:bg-gray-200 transition">
                    <FaGithub size={20} className="text-gray-700" />
                  </div>
                  <div>
                    <div className="font-medium text-gray-800">GitHub</div>
                    <div className="text-sm text-gray-600">Lihat kode saya</div>
                  </div>
                </a>

                <a href="https://linkedin.com/in/your_profile" className="flex items-center gap-4 p-3 rounded-lg hover:bg-blue-50 transition group">
                  <div className="bg-blue-100 p-2 rounded-full group-hover:bg-blue-200 transition">
                    <FaLinkedin size={20} className="text-blue-600" />
                  </div>
                  <div>
                    <div className="font-medium text-gray-800">LinkedIn</div>
                    <div className="text-sm text-gray-600">Koneksi profesional</div>
                  </div>
                </a>

                <a href="mailto:<EMAIL>" className="flex items-center gap-4 p-3 rounded-lg hover:bg-red-50 transition group">
                  <div className="bg-red-100 p-2 rounded-full group-hover:bg-red-200 transition">
                    <FaEnvelope size={20} className="text-red-600" />
                  </div>
                  <div>
                    <div className="font-medium text-gray-800">Email</div>
                    <div className="text-sm text-gray-600"><EMAIL></div>
                  </div>
                </a>
              </div>
            </div>
          </div>

          <div className="w-full md:w-1/2" data-aos="fade-left">
            <div className="bg-white p-6 rounded-xl shadow-lg">
              <h3 className="text-xl font-bold mb-4 text-gray-800">Kirim Pesan 📝</h3>
              <form className="grid gap-4" onSubmit={handleSubmit}>
                <input
                  type="text"
                  placeholder="Nama Lengkap"
                  className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                  value={nama}
                  onChange={(e) => setNama(e.target.value)}
                />
                <textarea
                  placeholder="Pesan"
                  rows="5"
                  className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent resize-none"
                  value={pesan}
                  onChange={(e) => setPesan(e.target.value)}
                />
                <button
                  type="submit"
                  className="bg-red-500 text-white py-3 px-6 rounded-lg hover:bg-red-600 transition-colors font-medium flex items-center justify-center gap-2"
                >
                  <span>📤</span> Kirim Pesan
                </button>
              </form>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center mt-16 pt-8 border-t border-pink-200">
          <p className="text-gray-600 flex items-center justify-center gap-2">
            Dibuat dengan <FaHeart className="text-red-500" /> oleh Developer Super Lucu
          </p>
          <p className="text-sm text-gray-500 mt-2">
            Designed by 🌟 Readly
          </p>
        </div>
      </section>
    </div>
  );
}

export default App;