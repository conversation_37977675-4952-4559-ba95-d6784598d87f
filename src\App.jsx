import React, { useEffect, useState } from 'react';
import AOS from 'aos';
import 'aos/dist/aos.css';
import gsap from 'gsap';
import { ScrollToPlugin } from 'gsap/ScrollToPlugin';
import { AboutImage, CVImage } from './assets';
import { FaFigma, FaReact, FaJs, FaWhatsapp, FaGithub, FaLinkedin, FaEnvelope, FaHeart } from "react-icons/fa";
import { BiLogoTailwindCss } from "react-icons/bi";
import { VscVscode } from "react-icons/vsc";

gsap.registerPlugin(ScrollToPlugin);

function App() {
  const [nama, setNama] = useState("");
  const [pesan, setPesan] = useState("");

  const handleSubmit = (e) => {
    e.preventDefault();
    const noWhatsApp = "+6283846795074"; // Ganti dengan nomor tujuan tanpa +
    const teks = `Halo, saya ${nama}.\n${pesan}`;
    const url = `https://api.whatsapp.com/send?phone=${noWhatsApp}&text=${encodeURIComponent(teks)}`;
    window.open(url, "_blank");
  };

  useEffect(() => {
    AOS.init({ duration: 1000, once: true });
  }, []);

  useEffect(() => {
    // Scroll animation
    const links = document.querySelectorAll("nav a[href^='#']");
    links.forEach(link => {
      link.addEventListener('click', e => {
        e.preventDefault();
        const target = document.querySelector(link.getAttribute('href'));
        if (target) {
          gsap.to(window, {
            duration: 1,
            scrollTo: { y: target, offsetY: 50 },
            ease: "power2.out"
          });
        }
      });
    });

    return () => {
      links.forEach(link => {
        link.removeEventListener('click', () => { });
      });
    };
  }, []);

  const tools = [
    <FaFigma />, <VscVscode />, <FaReact />, <BiLogoTailwindCss />, <FaJs />, <FaGithub />,
  ];

  const projects = [
    { title: 'Math Brain', desc: 'Lorem ipsum dolor sit amet, consec.', link: '#' },
    { title: 'NatureQuest', desc: 'Lorem ipsum dolor sit amet, consec.', link: '#' },
    { title: 'Tangkap Lalat', desc: 'Lorem ipsum dolor sit amet, consec.', link: '#' }
  ];

  return (
    <div className="min-h-screen bg-blue-50 font-sans text-gray-800">
      {/* Navbar */}
      <nav className="sticky top-0 z-50 bg-white shadow-md py-4 px-6 flex justify-end gap-6 text-sm font-medium text-gray-700">
        <a href="#home" className="hover:text-blue-500 transition">Home</a>
        <a href="#about" className="hover:text-blue-500 transition">About</a>
        <a href="#projects" className="hover:text-blue-500 transition">Project</a>
        <a href="#contact" className="hover:text-blue-500 transition">Contact</a>
      </nav>

      {/* Hero Section */}
      <section id="home" className="flex flex-col-reverse md:flex-row items-center justify-center px-8 py-16 gap-20">
        <div className="max-w-xl" data-aos="fade-right">
          <div className="mb-2 text-sm bg-blue-100 text-blue-700 inline-block px-3 py-3 rounded-full">
            <FaHeart className="inline mr-1" /> Kode yang indah, lahir dari <strong>ketekunan</strong>.
          </div>
          <h1 className="text-4xl font-bold mb-4">Halo, Saya Ella</h1>
          <p className="text-gray-600 mb-4">
            Aku suka bikin hal lucu & interaktif <FaHeart className="inline mr-1" /> <br />
            Seperti halaman ini contohnya! Selain coding, saya juga menyukai tools berikut.
          </p>
          <a
            href={CVImage}
            download="CV_Ella.png"
            className="inline-block mt-4 px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            Download CV
          </a>
        </div>
        <img
          src={AboutImage}
          alt="Ella"
          className="w-48 h-48 md:w-60 md:h-80 object-cover rounded-full border-4 border-blue-200 shadow-md"
          data-aos="zoom-in"
        />
      </section>

      {/* About */}
      <section id="about" className="px-8 py-16 md:py-26 bg-white">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-2xl font-semibold mb-8" data-aos="fade-down">About Me</h2>
          <div className="flex flex-col md:flex-row items-center gap-10">
            <img
              src={AboutImage}
              alt="About"
              className="w-60 h-60 object-cover rounded-full border-2 border-blue-200"
              data-aos="zoom-in"
            />
            <div>
              <p className="mb-4" data-aos="fade-left">
                Aku suka bikin hal lucu & Interaktif <FaHeart className="inline mr-1 text-blue-500" /> <br />
                Seperti halaman ini contohnya! Selain coding, saya juga menyukai tools berikut.
              </p>
              <div className="flex gap-4 text-2xl text-blue-500" data-aos="fade-up">
                {tools.map((icon, index) => (
                  <span key={index} className="hover:text-blue-700 transition-transform transform hover:scale-110">
                    {icon}
                  </span>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>


      {/* Projects */}
      <section id="projects" className="px-8 md:px-16 py-16">
        <h2 className="text-2xl font-semibold mb-8 text-center" data-aos="fade-down">My Project</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {projects.map((project, idx) => (
            <div key={idx} className="bg-blue-100 p-6 rounded-xl shadow hover:shadow-lg transition" data-aos="zoom-in" data-aos-delay={idx * 100}>
              <h3 className="font-semibold text-lg mb-2">{project.title}</h3>
              <p className="text-sm text-gray-700 mb-4">{project.desc}</p>
              <a href={project.link} className="inline-block px-4 py-2 text-sm bg-blue-500 text-white rounded-full hover:bg-blue-600 transition">
                Lihat Game
              </a>
            </div>
          ))}
        </div>
      </section>

      {/* Contact */}
      <section id="contact" className="px-8 md:px-20 py-16 bg-white">
        <h2 className="text-2xl font-semibold mb-8 text-center" data-aos="fade-up">Contact</h2>

        <div className="flex flex-col md:flex-row gap-10 justify-center items-start max-w-4xl mx-auto">
          <div className="flex flex-col gap-6 w-full md:w-1/2" data-aos="fade-right">
            <a href="https://wa.me/+6283846795074" className="flex items-center gap-4 text-gray-700 hover:text-green-500 transition">
              <FaWhatsapp size={24} />
              <span>+62 838-4679-5074</span>
            </a>
            <a href="https://github.com/your_username" className="flex items-center gap-4 text-gray-700 hover:text-black transition">
              <FaGithub size={24} />
              <span>GitHub</span>
            </a>
            <a href="https://linkedin.com/in/your_profile" className="flex items-center gap-4 text-gray-700 hover:text-blue-700 transition">
              <FaLinkedin size={24} />
              <span>LinkedIn</span>
            </a>
            <a href="mailto:<EMAIL>" className="flex items-center gap-4 text-gray-700 hover:text-red-500 transition">
              <FaEnvelope size={24} />
              <span>Email</span>
            </a>
          </div>

          <form className="w-full md:w-1/2 grid gap-4" data-aos="fade-left" onSubmit={handleSubmit}>
            <input type="text" placeholder="Nama Lengkap" className="w-full px-4 py-2 border rounded" value={nama} onChange={(e) => setNama(e.target.value)} />
            <textarea placeholder="Pesan" rows="4" className="w-full px-4 py-2 border rounded" value={pesan} onChange={(e) => setPesan(e.target.value)} />
            <button type="submit" className="bg-blue-500 text-white py-2 px-6 rounded hover:bg-blue-600 transition">
              Kirim Pesan
            </button>
          </form>
        </div>
      </section>
    </div>
  );
}

export default App;